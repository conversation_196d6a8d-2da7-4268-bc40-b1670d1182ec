# frozen_string_literal: true

RSpec.describe Sentry::Configuration do
  describe "#structured_logger_class" do
    it "defaults to nil" do
      expect(subject.structured_logger_class).to be_nil
    end
  end

  describe "#structured_logger_class=" do
    it "accepts class argument" do
      subject.structured_logger_class = Sentry::DebugStructuredLogger

      expect(subject.structured_logger_class).to eq(Sentry::DebugStructuredLogger)
    end

    it "doesn't accept non-class argument" do
      expect { subject.structured_logger_class = "foo" }.to raise_error(
        Sentry::Error, 
        "config.structured_logger_class must be a class. got: String"
      )
    end
  end

  describe "logger initialization with custom structured_logger_class" do
    before do
      perform_basic_setup do |config|
        config.enable_logs = true
        config.structured_logger_class = Sentry::DebugStructuredLogger
      end
    end

    after do
      # Reset the logger instance variable
      Sentry.instance_variable_set(:@logger, nil)
    end

    it "uses the configured structured logger class" do
      logger = Sentry.logger
      expect(logger).to be_a(Sentry::DebugStructuredLogger)
    end

    it "creates a working debug structured logger" do
      logger = Sentry.logger
      logger.clear # Clear any existing events

      logger.info("Test message", test_attr: "test_value")

      logged_events = logger.logged_events
      expect(logged_events).not_to be_empty

      log_event = logged_events.last
      expect(log_event["message"]).to eq("Test message")
      expect(log_event["level"]).to eq("info")
      expect(log_event["attributes"]["test_attr"]).to eq("test_value")
    end
  end

  describe "logger initialization with default structured_logger_class" do
    before do
      perform_basic_setup do |config|
        config.enable_logs = true
        # Don't set structured_logger_class, should default to StructuredLogger
      end
    end

    after do
      # Reset the logger instance variable
      Sentry.instance_variable_set(:@logger, nil)
    end

    it "uses the default StructuredLogger class" do
      logger = Sentry.logger
      expect(logger).to be_a(Sentry::StructuredLogger)
    end
  end
end
