# frozen_string_literal: true

require "sentry/rails/log_subscriber"

module Sentry
  module Rails
    module LogSubscribers
      # LogSubscriber for ActiveJob events that captures background job execution
      # and logs them using Sentry's structured logging system.
      #
      # This subscriber captures various ActiveJob events including job execution,
      # enqueueing, retries, and failures with relevant job information.
      #
      # @example Usage
      #   # Enable structured logging for ActiveJob
      #   Sentry.init do |config|
      #     config.enable_logs = true
      #     config.rails.structured_logging = true
      #     config.rails.structured_logging.attach_to = [:active_job]
      #   end
      class ActiveJobSubscriber < Sentry::Rails::LogSubscriber
        # Handle perform.active_job events
        #
        # @param event [ActiveSupport::Notifications::Event] The job performance event
        def perform(event)
          return if excluded_event?(event)

          job = event.payload[:job]
          duration = duration_ms(event)

          # Prepare structured attributes
          attributes = {
            job_class: job.class.name,
            job_id: job.job_id,
            queue_name: job.queue_name,
            duration_ms: duration,
            executions: job.executions,
            priority: job.priority
          }

          # Add adapter information if available
          attributes[:adapter] = job.class.queue_adapter.class.name if job.class.respond_to?(:queue_adapter)

          # Add scheduled time information
          if job.scheduled_at
            attributes[:scheduled_at] = job.scheduled_at.iso8601
            attributes[:delay_ms] = ((Time.current - job.scheduled_at) * 1000).round(2)
          end

          # Add arguments if PII is allowed
          if Sentry.configuration.send_default_pii && job.arguments.present?
            # Filter sensitive arguments
            filtered_args = filter_sensitive_arguments(job.arguments)
            attributes[:arguments] = filtered_args unless filtered_args.empty?
          end

          message = "Job performed: #{job.class.name}"
          level = level_for_duration(duration, 30000) # 30 second threshold for job execution

          # Log the structured event
          log_structured_event(
            message: message,
            level: level,
            attributes: attributes
          )
        end

        # Handle enqueue.active_job events
        #
        # @param event [ActiveSupport::Notifications::Event] The job enqueue event
        def enqueue(event)
          return if excluded_event?(event)

          job = event.payload[:job]

          # Prepare structured attributes
          attributes = {
            job_class: job.class.name,
            job_id: job.job_id,
            queue_name: job.queue_name,
            priority: job.priority
          }

          # Add adapter information if available
          attributes[:adapter] = job.class.queue_adapter.class.name if job.class.respond_to?(:queue_adapter)

          # Add scheduled time if it's a delayed job
          if job.scheduled_at
            attributes[:scheduled_at] = job.scheduled_at.iso8601
            attributes[:delay_seconds] = (job.scheduled_at - Time.current).round(2)
          end

          message = "Job enqueued: #{job.class.name}"

          # Log the structured event
          log_structured_event(
            message: message,
            level: :info,
            attributes: attributes
          )
        end

        # Handle retry_stopped.active_job events
        #
        # @param event [ActiveSupport::Notifications::Event] The job retry stopped event
        def retry_stopped(event)
          return if excluded_event?(event)

          job = event.payload[:job]
          error = event.payload[:error]

          # Prepare structured attributes
          attributes = {
            job_class: job.class.name,
            job_id: job.job_id,
            queue_name: job.queue_name,
            executions: job.executions,
            error_class: error.class.name,
            error_message: error.message
          }

          message = "Job retry stopped: #{job.class.name}"

          # Log the structured event
          log_structured_event(
            message: message,
            level: :error,
            attributes: attributes
          )
        end

        # Handle discard.active_job events
        #
        # @param event [ActiveSupport::Notifications::Event] The job discard event
        def discard(event)
          return if excluded_event?(event)

          job = event.payload[:job]
          error = event.payload[:error]

          # Prepare structured attributes
          attributes = {
            job_class: job.class.name,
            job_id: job.job_id,
            queue_name: job.queue_name,
            executions: job.executions
          }

          attributes[:error_class] = error.class.name if error
          attributes[:error_message] = error.message if error

          message = "Job discarded: #{job.class.name}"

          # Log the structured event
          log_structured_event(
            message: message,
            level: :warn,
            attributes: attributes
          )
        end

        private

        # Filter sensitive arguments from job arguments
        #
        # @param arguments [Array] Job arguments
        # @return [Array] Filtered arguments
        def filter_sensitive_arguments(arguments)
          return [] unless arguments.is_a?(Array)

          arguments.map do |arg|
            case arg
            when Hash
              filter_sensitive_hash(arg)
            when String
              # Don't include long strings that might contain sensitive data
              arg.length > 100 ? "[FILTERED: #{arg.length} chars]" : arg
            else
              arg
            end
          end
        end

        # Filter sensitive data from hash arguments
        #
        # @param hash [Hash] Hash argument
        # @return [Hash] Filtered hash
        def filter_sensitive_hash(hash)
          sensitive_keys = %w[
            password token secret api_key
            email personal_data user_data
            credit_card ssn social_security_number
          ]

          hash.reject do |key, _value|
            key_str = key.to_s.downcase
            sensitive_keys.any? { |sensitive| key_str.include?(sensitive) }
          end
        end
      end
    end
  end
end
