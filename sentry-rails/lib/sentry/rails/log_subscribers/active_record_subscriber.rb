# frozen_string_literal: true

require "sentry/rails/log_subscriber"

module Sentry
  module Rails
    module LogSubscribers
      # LogSubscriber for ActiveRecord events that captures database queries
      # and logs them using Sentry's structured logging system.
      #
      # This subscriber captures sql.active_record events and formats them
      # with relevant database information including SQL queries, duration,
      # database configuration, and caching information.
      #
      # @example Usage
      #   # Automatically attached when structured logging is enabled for :active_record
      #   Sentry.init do |config|
      #     config.enable_logs = true
      #     config.rails.structured_logging = true
      #     config.rails.structured_logging.attach_to = [:active_record]
      #   end
      class ActiveRecordSubscriber < Sentry::Rails::LogSubscriber
        # ActiveRecord-specific excluded events
        EXCLUDED_EVENTS = ["SCHEMA", "TRANSACTION"].freeze

        # Handle sql.active_record events
        #
        # @param event [ActiveSupport::Notifications::Event] The SQL event
        def sql(event)
          return if excluded_event?(event)

          # Extract relevant data from the event
          sql = event.payload[:sql]
          statement_name = event.payload[:name]
          cached = event.payload.fetch(:cached, false)
          connection_id = event.payload[:connection_id]
          duration = duration_ms(event)

          # Get database configuration if available
          db_config = extract_db_config(event.payload)

          # Prepare structured attributes
          attributes = {
            sql: sql,
            duration_ms: duration,
            cached: cached
          }

          # Add optional attributes
          attributes[:statement_name] = statement_name if statement_name && statement_name != "SQL"
          attributes[:connection_id] = connection_id if connection_id

          # Add database configuration attributes
          add_db_config_attributes(attributes, db_config)

          # Determine log message and level
          message = build_log_message(statement_name)
          level = level_for_duration(duration)

          # Log the structured event
          log_structured_event(
            message: message,
            level: level,
            attributes: attributes
          )
        end

        protected

        # Check if an ActiveRecord event should be excluded from logging
        #
        # @param event [ActiveSupport::Notifications::Event] The event to check
        # @return [Boolean] true if the event should be excluded
        def excluded_event?(event)
          # Call parent method for common exclusions
          return true if super

          # Skip ActiveRecord-specific excluded event types
          return true if EXCLUDED_EVENTS.include?(event.payload[:name])

          false
        end

        private

        # Build a descriptive log message
        #
        # @param statement_name [String, nil] The SQL statement name
        # @return [String] The log message
        def build_log_message(statement_name)
          if statement_name && statement_name != "SQL"
            "Database query: #{statement_name}"
          else
            "Database query"
          end
        end

        # Extract database configuration from a connection payload
        #
        # @param payload [Hash] The event payload
        # @return [Hash, nil] Database configuration hash or nil if not available
        def extract_db_config(payload)
          connection = payload[:connection]

          if payload[:connection_id] && !connection
            # Fallback to base connection on Rails < 6.0.0
            connection = ActiveRecord::Base.connection_pool.connections.find do |conn|
              conn.object_id == payload[:connection_id]
            end
          end

          return nil unless connection

          if connection.pool.respond_to?(:db_config)
            connection.pool.db_config.configuration_hash
          elsif connection.pool.respond_to?(:spec)
            connection.pool.spec.config
          end
        rescue => e
          # Silently handle any errors in extracting db config
          Sentry.configuration.sdk_logger.debug("Failed to extract db config: #{e.message}")
          nil
        end

        # Add database configuration attributes to the attributes hash
        #
        # @param attributes [Hash] The attributes hash to modify
        # @param db_config [Hash] The database configuration
        def add_db_config_attributes(attributes, db_config)
          return unless db_config

          attributes[:db_system] = db_config[:adapter] if db_config[:adapter]
          attributes[:db_name] = db_config[:database] if db_config[:database]
          attributes[:server_address] = db_config[:host] if db_config[:host]
          attributes[:server_port] = db_config[:port] if db_config[:port]
          attributes[:server_socket_address] = db_config[:socket] if db_config[:socket]
        end
      end
    end
  end
end
