# frozen_string_literal: true

require "spec_helper"

RSpec.describe Sentry::Rails::LogSubscriber do
  describe "base functionality" do
    let(:subscriber) { described_class.new }

    describe "#excluded_event?" do
      it "excludes events starting with !" do
        event = double("event", name: "!connection.active_record", payload: {})
        expect(subscriber.send(:excluded_event?, event)).to be true
      end

      it "allows normal events" do
        event = double("event", name: "sql.active_record", payload: { name: "User Load" })
        expect(subscriber.send(:excluded_event?, event)).to be false
      end
    end

    describe "#duration_ms" do
      it "calculates duration in milliseconds" do
        event = double("event", duration: 123.456789)
        expect(subscriber.send(:duration_ms, event)).to eq(123.46)
      end
    end

    describe "#level_for_duration" do
      it "returns info for fast operations" do
        expect(subscriber.send(:level_for_duration, 500)).to eq(:info)
      end

      it "returns warn for slow operations" do
        expect(subscriber.send(:level_for_duration, 1500)).to eq(:warn)
      end

      it "uses custom threshold" do
        expect(subscriber.send(:level_for_duration, 500, 300)).to eq(:warn)
      end
    end

    describe "#log_structured_event" do
      before do
        make_basic_app do |config|
          config.enable_logs = true
        end
      end

      it "logs events through Sentry.logger" do
        expect(Sentry.logger).to receive(:info).with("Test message", test_attr: "value")

        subscriber.send(:log_structured_event,
          message: "Test message",
          level: :info,
          attributes: { test_attr: "value" }
        )
      end

      it "handles errors gracefully" do
        allow(Sentry.logger).to receive(:info).and_raise(StandardError.new("Test error"))

        expect {
          subscriber.send(:log_structured_event,
            message: "Test message",
            level: :info,
            attributes: {}
          )
        }.not_to raise_error
      end
    end
  end

  describe "attach_to behavior" do
    it "sets logger to nil to prevent standard Rails logging" do
      subscriber_class = Class.new(described_class)
      subscriber_class.attach_to :test_component

      expect(subscriber_class.logger).to be_nil
    end
  end
end
