# frozen_string_literal: true

require "spec_helper"

RSpec.describe Sentry::Rails::LogSubscribers::ActiveRecordSubscriber do
  before do
    make_basic_app
  end

  let(:subscriber) { described_class.new }

  describe "#excluded_event?" do
    it "excludes events starting with !" do
      event = double("event", name: "!connection.active_record", payload: {})
      expect(subscriber.send(:excluded_event?, event)).to be true
    end

    it "excludes SCHEMA events" do
      event = double("event", name: "sql.active_record", payload: { name: "SCHEMA" })
      expect(subscriber.send(:excluded_event?, event)).to be true
    end

    it "excludes TRANSACTION events" do
      event = double("event", name: "sql.active_record", payload: { name: "TRANSAC<PERSON>ON" })
      expect(subscriber.send(:excluded_event?, event)).to be true
    end

    it "allows normal events" do
      event = double("event", name: "sql.active_record", payload: { name: "User Load" })
      expect(subscriber.send(:excluded_event?, event)).to be false
    end
  end

  describe "#extract_db_config" do
    let(:connection) { double("connection") }
    let(:pool) { double("pool") }
    let(:db_config) { double("db_config") }
    let(:config_hash) { { adapter: "sqlite3", database: "test.db" } }

    before do
      allow(connection).to receive(:pool).and_return(pool)
    end

    context "when connection has db_config" do
      before do
        allow(pool).to receive(:respond_to?).with(:db_config).and_return(true)
        allow(pool).to receive(:db_config).and_return(db_config)
        allow(db_config).to receive(:configuration_hash).and_return(config_hash)
      end

      it "extracts db config from connection" do
        payload = { connection: connection }
        result = subscriber.send(:extract_db_config, payload)
        expect(result).to eq(config_hash)
      end
    end

    context "when connection has spec" do
      before do
        allow(pool).to receive(:respond_to?).with(:db_config).and_return(false)
        allow(pool).to receive(:respond_to?).with(:spec).and_return(true)
        allow(pool).to receive(:spec).and_return(db_config)
        allow(db_config).to receive(:config).and_return(config_hash)
      end

      it "extracts db config from spec" do
        payload = { connection: connection }
        result = subscriber.send(:extract_db_config, payload)
        expect(result).to eq(config_hash)
      end
    end

    context "when connection is nil but connection_id is present" do
      let(:connection_pool) { double("connection_pool") }
      let(:connections) { [connection] }

      before do
        allow(ActiveRecord::Base).to receive(:connection_pool).and_return(connection_pool)
        allow(connection_pool).to receive(:connections).and_return(connections)
        allow(connection).to receive(:object_id).and_return(12345)
        allow(pool).to receive(:respond_to?).with(:db_config).and_return(true)
        allow(pool).to receive(:db_config).and_return(db_config)
        allow(db_config).to receive(:configuration_hash).and_return(config_hash)
      end

      it "finds connection by id and extracts config" do
        payload = { connection: nil, connection_id: 12345 }
        result = subscriber.send(:extract_db_config, payload)
        expect(result).to eq(config_hash)
      end
    end

    context "when extraction fails" do
      before do
        allow(pool).to receive(:respond_to?).and_raise(StandardError.new("Test error"))
      end

      it "returns nil and logs error" do
        payload = { connection: connection }
        expect(Sentry.configuration.sdk_logger).to receive(:debug).with(/Failed to extract db config/)
        result = subscriber.send(:extract_db_config, payload)
        expect(result).to be_nil
      end
    end
  end

  describe "#add_db_config_attributes" do
    let(:attributes) { {} }
    let(:db_config) do
      {
        adapter: "postgresql",
        database: "myapp_test",
        host: "localhost",
        port: 5432,
        socket: "/tmp/socket"
      }
    end

    it "adds database configuration attributes" do
      subscriber.send(:add_db_config_attributes, attributes, db_config)

      expect(attributes[:db_system]).to eq("postgresql")
      expect(attributes[:db_name]).to eq("myapp_test")
      expect(attributes[:server_address]).to eq("localhost")
      expect(attributes[:server_port]).to eq(5432)
      expect(attributes[:server_socket_address]).to eq("/tmp/socket")
    end

    it "handles nil db_config" do
      subscriber.send(:add_db_config_attributes, attributes, nil)
      expect(attributes).to be_empty
    end

    it "handles partial db_config" do
      partial_config = { adapter: "sqlite3" }
      subscriber.send(:add_db_config_attributes, attributes, partial_config)

      expect(attributes[:db_system]).to eq("sqlite3")
      expect(attributes).not_to have_key(:db_name)
      expect(attributes).not_to have_key(:server_address)
    end
  end

  describe "#build_log_message" do
    it "builds message with statement name" do
      message = subscriber.send(:build_log_message, "User Load")
      expect(message).to eq("Database query: User Load")
    end

    it "builds generic message for SQL" do
      message = subscriber.send(:build_log_message, "SQL")
      expect(message).to eq("Database query")
    end

    it "builds generic message for nil statement name" do
      message = subscriber.send(:build_log_message, nil)
      expect(message).to eq("Database query")
    end
  end
end
